package com.whiskerguard.ai.service.invocation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.AiRequest;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import com.whiskerguard.ai.repository.AiRequestRepository;
import com.whiskerguard.ai.repository.AiToolRepository;
import com.whiskerguard.ai.service.AiToolMetricsService;
import com.whiskerguard.ai.service.LawReferenceEnhancer;
import com.whiskerguard.ai.service.SensitiveWordFilterService;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiRequestDTO;
import com.whiskerguard.ai.service.dto.EnhancedAiResponse;
import com.whiskerguard.ai.service.exception.AiInvocationException;
import com.whiskerguard.ai.service.mapper.AiRequestMapper;
import com.whiskerguard.ai.service.prompt.PromptBuildRequest;
import com.whiskerguard.ai.service.prompt.PromptBuilderService;
import com.whiskerguard.ai.service.workflow.AiWorkflow;
import com.whiskerguard.ai.service.workflow.AiWorkflowResult;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * AI 调用主要业务服务：记录请求、触发工作流、保存响应和指标
 */
@Service
@Transactional
public class AiInvocationService {

    private static final Logger log = LoggerFactory.getLogger(AiInvocationService.class);

    private final AiRequestRepository requestRepo;
    private final AiRequestMapper requestMapper;
    private final AiToolMetricsService metricsService;
    private final ObjectMapper objectMapper;
    private final AiToolRepository toolRepo;
    private final WorkflowClient workflowClient;
    private final SensitiveWordFilterService sensitiveWordFilterService;
    private final PromptBuilderService promptBuilderService;
    private final LawReferenceEnhancer lawReferenceEnhancer;

    @Value("${temporal.task-queue:AiTaskQueue}")
    private String taskQueue;

    public AiInvocationService(
        AiRequestRepository requestRepo,
        AiRequestMapper requestMapper,
        AiToolMetricsService metricsService,
        ObjectMapper objectMapper,
        AiToolRepository toolRepo,
        WorkflowClient workflowClient,
        SensitiveWordFilterService sensitiveWordFilterService,
        PromptBuilderService promptBuilderService,
        LawReferenceEnhancer lawReferenceEnhancer
    ) {
        this.requestRepo = requestRepo;
        this.requestMapper = requestMapper;
        this.metricsService = metricsService;
        this.objectMapper = objectMapper;
        this.toolRepo = toolRepo;
        this.workflowClient = workflowClient;
        this.sensitiveWordFilterService = sensitiveWordFilterService;
        this.promptBuilderService = promptBuilderService;
        this.lawReferenceEnhancer = lawReferenceEnhancer;
    }

    /**
     * 发起 AI 调用，记录请求和响应，并保存指标
     * 优化事务管理，分离数据库操作和外部调用以提高性能
     * 支持模板化提示词构建
     */
    public AiRequestDTO invoke(AiInvocationRequestDTO dto) {
        log.info("开始处理AI调用请求，工具类型: {}, 员工ID: {}, 使用模板: {}", dto.getToolKey(), dto.getEmployeeId(), dto.isUseTemplate());

        // 1. 参数验证
        validateInvocationRequest(dto);

        // 2. 处理模板化提示词（如果启用）
        AiInvocationRequestDTO processedDto = processTemplateIfNeeded(dto);

        // 3. 查找AI工具配置
        AiTool aiTool = findAiToolByKey(processedDto.getToolKey(), processedDto);
        log.debug("找到AI工具配置: {}", aiTool.getName());

        // 4. 创建并持久化请求记录（单独事务）
        AiRequest req = createAndSaveAiRequest(processedDto, aiTool);
        log.debug("已保存AI请求记录，ID: {}", req.getId());

        // 5. 通过 Temporal 工作流执行调用
        String workflowId = "ai-workflow-" + UUID.randomUUID();
        AiWorkflow workflow = workflowClient.newWorkflowStub(
            AiWorkflow.class,
            WorkflowOptions.newBuilder()
                .setTaskQueue(taskQueue)
                .setWorkflowId(workflowId)
                // 优化工作流超时设置 - 减少到 3 分钟，提高响应速度
                .setWorkflowExecutionTimeout(java.time.Duration.ofMinutes(3))
                // 设置工作流运行超时为 2.5 分钟
                .setWorkflowRunTimeout(java.time.Duration.ofSeconds(150))
                .build()
        );

        AiWorkflowResult result = null;
        try {
            // 6. 同步执行工作流
            log.info("开始执行AI工作流，工作流ID: {}", workflowId);
            result = workflow.execute(processedDto);

            // 7. 更新请求为成功状态
            updateRequestWithSuccess(req, result);
            log.info(
                "AI调用成功完成，请求ID: {}, 响应长度: {}",
                req.getId(),
                result.getContent() != null ? result.getContent().length() : 0
            );

            // 8. 法规引用增强处理（如果需要）
            if (shouldEnhanceWithLawReferences(processedDto)) {
                enhanceResponseWithLawReferences(req, processedDto.getTenantId());
            }
        } catch (Exception ex) {
            // 9. 处理调用失败
            log.error("AI调用失败，请求ID: {}, 错误: {}", req.getId(), ex.getMessage(), ex);
            updateRequestWithError(req, ex);

            // 抛出业务异常，触发事务回滚
            throw new AiInvocationException("AI调用失败: " + ex.getMessage(), processedDto.getToolKey(), "WORKFLOW_EXECUTION_FAILED", ex);
        }

        // 10. 保存指标（只有成功时才保存）
        if (result != null) {
            try {
                metricsService.saveAiCallMetrics(processedDto.getToolKey(), result, req);
                log.debug("已保存AI调用指标");
            } catch (Exception ex) {
                log.warn("保存指标失败，但不影响主流程: {}", ex.getMessage());
            }
        }

        // 11. 返回 DTO
        return requestMapper.toDto(req);
    }

    /**
     * 处理模板化提示词（如果启用）
     * 如果启用了模板功能，将使用模板构建最终的提示词
     * 如果未启用模板功能，直接返回原始DTO
     */
    private AiInvocationRequestDTO processTemplateIfNeeded(AiInvocationRequestDTO dto) {
        if (!dto.isUseTemplate()) {
            log.debug("未启用模板功能，使用原始提示词");
            return dto;
        }

        try {
            log.info("启用模板功能，开始构建模板化提示词，模板键: {}, 模板类型: {}", dto.getTemplateKey(), dto.getTemplateType());

            // 构建模板请求
            PromptBuildRequest request = PromptBuildRequest.builder()
                .tenantId(dto.getTenantId())
                .variables(dto.getTemplateVariables() != null ? dto.getTemplateVariables() : Map.of())
                .enableRagEnhancement(true) // 默认启用RAG增强
                .useCache(true); // 默认使用缓存

            // 优先使用模板键，其次使用模板类型
            if (dto.getTemplateKey() != null && !dto.getTemplateKey().trim().isEmpty()) {
                request.templateKey(dto.getTemplateKey());
                log.debug("使用模板键: {}", dto.getTemplateKey());
            } else if (dto.getTemplateType() != null) {
                request.templateType(dto.getTemplateType());
                log.debug("使用模板类型: {}", dto.getTemplateType());
            } else {
                log.warn("启用了模板功能但未指定模板键或模板类型，将使用原始提示词");
                return dto;
            }

            request = request.build();

            // 构建模板化提示词
            String templatePrompt = promptBuilderService.buildPrompt(request);
            log.debug("模板化提示词构建成功，长度: {}", templatePrompt.length());

            // 创建新的DTO，使用模板化提示词
            AiInvocationRequestDTO processedDto = new AiInvocationRequestDTO(
                dto.getToolKey(),
                templatePrompt, // 使用模板化提示词
                dto.getMetadata(),
                dto.getTenantId(),
                dto.getEmployeeId(),
                dto.getTemplateKey(),
                dto.getTemplateType(),
                dto.getTemplateVariables(),
                dto.isUseTemplate()
            );

            log.info(
                "模板化提示词处理完成，原始长度: {}, 模板化长度: {}",
                dto.getPrompt() != null ? dto.getPrompt().length() : 0,
                templatePrompt.length()
            );

            return processedDto;
        } catch (Exception e) {
            log.error("模板化提示词构建失败，将使用原始提示词，错误: {}", e.getMessage(), e);
            // 如果模板构建失败，降级到原始提示词
            return dto;
        }
    }

    /**
     * 验证调用请求参数
     */
    private void validateInvocationRequest(AiInvocationRequestDTO dto) {
        if (dto.getToolKey() == null || dto.getToolKey().trim().isEmpty()) {
            throw new IllegalArgumentException("工具类型不能为空");
        }
        if (dto.getPrompt() == null || dto.getPrompt().trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }
        if (dto.getEmployeeId() == null) {
            throw new IllegalArgumentException("员工ID不能为空");
        }

        // 如果启用了模板功能，验证模板相关参数
        if (dto.isUseTemplate()) {
            if ((dto.getTemplateKey() == null || dto.getTemplateKey().trim().isEmpty()) && dto.getTemplateType() == null) {
                throw new IllegalArgumentException("启用模板功能时，必须指定模板键或模板类型");
            }
        }

        log.debug("请求参数验证通过");
    }

    /**
     * 判断是否需要进行法规引用增强
     */
    private boolean shouldEnhanceWithLawReferences(AiInvocationRequestDTO dto) {
        // 1. 检查是否使用了法律相关的模板
        if (dto.isUseTemplate() && isLawRelatedTemplate(dto)) {
            return true;
        }

        // 2. 检查提示词中是否包含法律相关关键词
        if (containsLawKeywords(dto.getPrompt())) {
            return true;
        }

        // 3. 检查元数据中是否指定了启用法规引用
        if (dto.getMetadata() != null &&
            Boolean.TRUE.equals(dto.getMetadata().get("enableLawReferences"))) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否为法律相关模板
     */
    private boolean isLawRelatedTemplate(AiInvocationRequestDTO dto) {
        if (dto.getTemplateType() != null) {
            String templateType = dto.getTemplateType().name();
            return templateType.contains("CONTRACT") ||
                   templateType.contains("POLICY") ||
                   templateType.contains("LEGAL") ||
                   templateType.contains("COMPLIANCE");
        }

        if (dto.getTemplateKey() != null) {
            String templateKey = dto.getTemplateKey().toUpperCase();
            return templateKey.contains("CONTRACT") ||
                   templateKey.contains("POLICY") ||
                   templateKey.contains("LEGAL") ||
                   templateKey.contains("LAW") ||
                   templateKey.contains("COMPLIANCE");
        }

        return false;
    }

    /**
     * 检查提示词中是否包含法律关键词
     */
    private boolean containsLawKeywords(String prompt) {
        if (prompt == null) {
            return false;
        }

        String[] lawKeywords = {
            "合同", "协议", "法律", "法规", "条例", "制度", "合规", "违约", "责任", "义务",
            "审查", "审核", "风险", "条款", "规定", "办法", "标准", "规范"
        };

        String lowerPrompt = prompt.toLowerCase();
        for (String keyword : lawKeywords) {
            if (lowerPrompt.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 对AI响应进行法规引用增强
     */
    private void enhanceResponseWithLawReferences(AiRequest request, Long tenantId) {
        try {
            log.debug("开始对AI响应进行法规引用增强，请求ID: {}", request.getId());

            // 获取AI响应内容
            String originalResponse = request.getResponse();
            if (originalResponse == null || originalResponse.trim().isEmpty()) {
                log.debug("AI响应为空，跳过法规引用增强");
                return;
            }

            // 调用法规引用增强器
            EnhancedAiResponse enhancedResponse = lawReferenceEnhancer
                .enhanceWithReferences(originalResponse, tenantId);

            // 更新响应内容
            if (enhancedResponse.getStatus() == EnhancedAiResponse.ProcessingStatus.SUCCESS) {
                request.setResponse(enhancedResponse.getEnhancedContent());

                // 保存增强后的请求
                requestRepo.save(request);

                log.info("法规引用增强成功，请求ID: {}, 引用数量: {}",
                        request.getId(),
                        enhancedResponse.getReferences() != null ? enhancedResponse.getReferences().size() : 0);
            } else {
                log.debug("法规引用增强未找到相关引用，请求ID: {}, 状态: {}",
                         request.getId(), enhancedResponse.getStatus());
            }

        } catch (Exception e) {
            log.warn("法规引用增强失败，但不影响主流程，请求ID: {}, 错误: {}",
                    request.getId(), e.getMessage());
        }
    }

    /**
     * 查找AI工具配置
     * 如果dto中的metadata包含isModel=true，则查询模型类型的工具
     * 否则查询非模型类型的工具
     */
    private AiTool findAiToolByKey(String toolKey, AiInvocationRequestDTO dto) {
        // 检查metadata中是否包含isModel字段
        boolean isModel = false;
        if (dto.getMetadata() != null && dto.getMetadata().containsKey("isModel")) {
            Object isModelValue = dto.getMetadata().get("isModel");
            // 处理不同类型的isModel值
            if (isModelValue instanceof Boolean) {
                isModel = (Boolean) isModelValue;
            } else if (isModelValue instanceof String) {
                isModel = Boolean.parseBoolean((String) isModelValue);
            }
            log.debug("从metadata中获取isModel值: {}", isModel);
        }

        // 根据isModel值选择不同的查询方法
        if (isModel) {
            return toolRepo
                .findByToolKeyAndIsDeletedFalse(toolKey)
                .filter(tool -> tool.getIsModel() != null && tool.getIsModel())
                .orElseThrow(() -> new AiInvocationException("未找到模型配置: " + toolKey, toolKey, "MODEL_NOT_FOUND"));
        } else {
            return toolRepo
                .findByToolKeyAndIsDeletedFalseAndIsModelFalse(toolKey)
                .orElseThrow(() -> new AiInvocationException("未找到工具配置: " + toolKey, toolKey, "TOOL_NOT_FOUND"));
        }
    }

    /**
     * 创建并保存AI请求记录（单独事务）
     */
    @Transactional
    public AiRequest createAndSaveAiRequest(AiInvocationRequestDTO dto, AiTool aiTool) {
        AiRequest req = createAiRequest(dto, aiTool);
        return requestRepo.save(req);
    }

    /**
     * 创建AI请求记录
     */
    private AiRequest createAiRequest(AiInvocationRequestDTO dto, AiTool aiTool) {
        AiRequest req = new AiRequest();
        req.setToolType(dto.getToolKey());
        req.setPrompt(dto.getPrompt());
        req.setResponse(""); // 初始化为空字符串
        req.setTool(aiTool); // 设置工具关联

        // 设置必填的时间字段
        Instant now = Instant.now();
        req.setCreatedAt(now);
        req.setUpdatedAt(now);
        req.setRequestTime(now);

        // 设置必填的状态和版本字段
        req.setStatus(RequestStatus.PROCESSING);
        req.setVersion(1);

        // 设置必填的ID字段
        req.setTenantId(dto.getTenantId());
        req.setEmployeeId(dto.getEmployeeId());

        // 设置软删除标志
        req.setIsDeleted(false);

        // 设置可选的元数据
        try {
            req.setMetadata(dto.getMetadata() == null ? null : objectMapper.writeValueAsString(dto.getMetadata()));
        } catch (JsonProcessingException e) {
            log.warn("序列化元数据失败: {}", e.getMessage());
            req.setMetadata(null);
        }

        return req;
    }

    /**
     * 更新请求为成功状态（单独事务）
     */
    @Transactional
    public void updateRequestWithSuccess(AiRequest req, AiWorkflowResult result) {
        // 截断响应内容，确保不超过合理限制
        String content = result.getContent();
        // TEXT 字段可以存储更多内容，但仍然需要合理限制以避免过大的数据
        // 设置为 50KB 的限制，对于大多数 AI 响应来说应该足够了
        if (content != null && content.length() > 50000) {
            content = content.substring(0, 49997) + "...";
            log.warn("AI响应内容过长，已截断。原长度: {}, 截断后长度: {}", result.getContent().length(), content.length());
        }
        // 优化：异步进行敏感词过滤，不阻塞主流程
        // 先保存原始内容，后续异步更新过滤后的内容
        if (content != null && !content.isEmpty()) {
            log.debug("准备异步进行敏感词过滤，内容长度: {}", content.length());
            // 异步过滤敏感词
            asyncFilterSensitiveWords(req, content);
        }

        req.setResponse(content);
        req.setStatus(RequestStatus.SUCCESS);
        req.setResponseTime(Instant.now());
        req.setUpdatedAt(Instant.now());
        requestRepo.save(req);
    }

    /**
     * 更新请求为失败状态（单独事务）
     */
    @Transactional
    public void updateRequestWithError(AiRequest req, Exception e) {
        // 截断错误信息，确保不超过合理限制
        String errorMessage = e.getMessage();
        // TEXT 字段可以存储更多内容，但错误信息通常不会太长
        // 设置为 10KB 的限制，对于错误信息来说应该足够了
        if (errorMessage != null && errorMessage.length() > 10000) {
            errorMessage = errorMessage.substring(0, 9997) + "...";
            log.warn("错误信息过长，已截断。原长度: {}, 截断后长度: {}", e.getMessage().length(), errorMessage.length());
        }

        req.setStatus(RequestStatus.FAILED);
        req.setErrorMessage(errorMessage);
        req.setResponseTime(Instant.now());
        req.setUpdatedAt(Instant.now());
        requestRepo.save(req);
    }

    /**
     * 异步进行敏感词过滤，不阻塞主流程
     */
    @Async
    public void asyncFilterSensitiveWords(AiRequest req, String originalContent) {
        try {
            log.debug("开始异步敏感词过滤，请求ID: {}", req.getId());
            String filteredContent = sensitiveWordFilterService.filterContent(originalContent);

            // 如果内容有变化，更新数据库
            if (!originalContent.equals(filteredContent)) {
                updateFilteredContent(req.getId(), filteredContent);
                log.debug("敏感词过滤完成并已更新，请求ID: {}", req.getId());
            } else {
                log.debug("内容无敏感词，无需更新，请求ID: {}", req.getId());
            }
        } catch (Exception e) {
            log.warn("异步敏感词过滤失败，请求ID: {}, 错误: {}", req.getId(), e.getMessage());
        }
    }

    /**
     * 更新过滤后的内容（单独事务）
     */
    @Transactional
    public void updateFilteredContent(Long requestId, String filteredContent) {
        requestRepo
            .findById(requestId)
            .ifPresent(req -> {
                req.setResponse(filteredContent);
                req.setUpdatedAt(Instant.now());
                requestRepo.save(req);
            });
    }
}
