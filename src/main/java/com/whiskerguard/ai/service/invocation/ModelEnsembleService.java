package com.whiskerguard.ai.service.invocation;

import com.whiskerguard.ai.domain.AiRequest;
import com.whiskerguard.ai.domain.AiTool;
import com.whiskerguard.ai.domain.enumeration.RequestStatus;
import com.whiskerguard.ai.repository.AiRequestRepository;
import com.whiskerguard.ai.service.AiToolMetricsService;
import com.whiskerguard.ai.service.AiToolService;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * 多模型集成服务
 * 该服务负责协调多个LLM模型的并行调用，收集各模型的输出结果，并通过结果整合引擎进行统一处理
 */
@Service
public class ModelEnsembleService {

    private final Logger log = LoggerFactory.getLogger(ModelEnsembleService.class);
    private final List<AiToolInvoker> modelInvokers;
    private final ResultIntegrationEngine integrationEngine;
    private final AiToolService aiToolService;
    private final AiToolMetricsService metricsService;
    private final AiRequestRepository requestRepository;
    private final Map<String, AiTool> toolConfigCache;

    /**
     * 构造函数，注入所有可用的模型调用器和结果整合引擎
     *
     * @param modelInvokers 所有可用的模型调用器列表
     * @param integrationEngine 结果整合引擎
     * @param aiToolService AI工具服务
     * @param metricsService AI工具指标服务
     * @param requestRepository AI请求存储库
     */
    public ModelEnsembleService(
        List<AiToolInvoker> modelInvokers,
        ResultIntegrationEngine integrationEngine,
        AiToolService aiToolService,
        AiToolMetricsService metricsService,
        AiRequestRepository requestRepository
    ) {
        this.modelInvokers = modelInvokers;
        this.integrationEngine = integrationEngine;
        this.aiToolService = aiToolService;
        this.metricsService = metricsService;
        this.requestRepository = requestRepository;

        // 初始化工具配置缓存
        this.toolConfigCache = new HashMap<>();

        // 获取所有可用的模型调用器的工具键
        modelInvokers.forEach(invoker -> {
            String toolKey = invoker.getToolKey();
            // 使用AiToolService直接查询AiTool实体
            AiTool tool = aiToolService.findByToolKey(toolKey);
            if (tool != null) {
                this.toolConfigCache.put(toolKey, tool);
            } else {
                log.warn("未能找到工具配置: {}", toolKey);
            }
        });

        log.info("ModelEnsembleService已初始化，可用模型数量：{}", modelInvokers.size());
    }

    /**
     * 生成集成响应，通过并行调用多个LLM模型，然后整合其结果
     *
     * @param request AI调用请求，包含提示词等信息
     * @param selectedModels 选择的模型名称列表
     * @return 整合后的响应结果
     */
    public String generateEnsembleResponse(AiInvocationRequestDTO request, List<String> selectedModels) {
        log.info("开始多模型集成调用，模型: {}, 员工ID: {}", selectedModels, request.getEmployeeId());

        // 记录开始时间
        Instant startTime = Instant.now();

        // 创建AI请求记录
        AiRequest aiRequest = createEnsembleRequest(request, selectedModels);
        aiRequest = requestRepository.save(aiRequest);
        log.debug("已保存多模型集成请求记录，ID: {}", aiRequest.getId());

        // 并行调用选定的模型
        List<CompletableFuture<ModelResponse>> futures = selectedModels
            .stream()
            .map(this::findInvokerByName)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .map(invoker ->
                CompletableFuture.supplyAsync(() -> {
                    long modelStartTime = System.currentTimeMillis();

                    // 获取对应的工具配置 - 修复：使用正确的方法获取AiTool
                    String toolKey = invoker.getToolKey();
                    AiTool toolConfig = toolConfigCache.get(toolKey);

                    // 如果缓存中没有，尝试通过名称查找
                    if (toolConfig == null) {
                        // 由于没有直接通过toolKey查找的方法，这里需要实现一个备选方案
                        // 可以考虑从缓存中重新加载或使用其他方式获取
                        log.warn("未在缓存中找到工具配置：{}，将使用默认配置", toolKey);
                        toolConfig = new AiTool(); // 创建一个简单的默认配置
                        toolConfig.setToolKey(toolKey);
                    }

                    // 正确传递两个参数
                    AiResult result = invoker.invoke(request, toolConfig);
                    String response = result.getContent();

                    long endTime = System.currentTimeMillis();
                    log.debug("模型 {} 响应时间: {} ms", invoker.getToolKey(), (endTime - modelStartTime));
                    return new ModelResponse(invoker.getToolKey(), response, (endTime - modelStartTime));
                })
            )
            .toList();

        // 收集所有模型的响应
        List<ModelResponse> responses = futures.stream().map(CompletableFuture::join).toList();

        log.info("已收集 {} 个模型的响应，请求的模型数量: {}", responses.size(), selectedModels.size());

        try {
            // 如果没有收集到任何响应，返回错误信息
            if (responses.isEmpty()) {
                log.error("没有从任何模型收集到响应");
                String errorMessage = "错误: 未能从所选模型生成任何响应。";

                // 更新请求为失败状态
                updateRequestWithError(aiRequest, new RuntimeException("没有从任何模型收集到响应"));

                return errorMessage;
            }

            // 整合结果
            String integratedResponse = integrationEngine.integrate(responses, request.getPrompt());

            // 计算总响应时间
            Instant endTime = Instant.now();
            long totalDurationMs = endTime.toEpochMilli() - startTime.toEpochMilli();

            // 更新请求为成功状态
            updateRequestWithSuccess(aiRequest, integratedResponse, endTime);

            // 保存集成调用指标
            saveEnsembleMetrics(request, selectedModels, aiRequest, totalDurationMs, responses);

            log.info("多模型集成调用成功完成，请求ID: {}, 总响应时间: {}ms", aiRequest.getId(), totalDurationMs);

            return integratedResponse;
        } catch (Exception e) {
            log.error("多模型集成调用失败，请求ID: {}, 错误: {}", aiRequest.getId(), e.getMessage(), e);

            // 更新请求为失败状态
            updateRequestWithError(aiRequest, e);

            throw new RuntimeException("多模型集成调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通过模型名称查找调用器
     *
     * @param modelName 模型名称
     * @return 包含调用器的Optional对象
     */
    private Optional<AiToolInvoker> findInvokerByName(String modelName) {
        return modelInvokers.stream().filter(invoker -> invoker.getToolKey().equalsIgnoreCase(modelName)).findFirst();
    }

    /**
     * 模型响应内部类，用于存储模型响应的相关信息
     */
    public static class ModelResponse {

        private final String modelName;
        private final String response;
        private final long responseTime;

        public ModelResponse(String modelName, String response, long responseTime) {
            this.modelName = modelName;
            this.response = response;
            this.responseTime = responseTime;
        }

        public String getModelName() {
            return modelName;
        }

        public String getResponse() {
            return response;
        }

        public long getResponseTime() {
            return responseTime;
        }
    }

    /**
     * 创建多模型集成请求记录
     */
    private AiRequest createEnsembleRequest(AiInvocationRequestDTO request, List<String> selectedModels) {
        AiRequest aiRequest = new AiRequest();
        aiRequest.setToolType("ensemble"); // 标记为集成调用
        aiRequest.setPrompt(request.getPrompt());
        aiRequest.setResponse(""); // 初始化为空字符串

        // 设置必填的时间字段
        Instant now = Instant.now();
        aiRequest.setCreatedAt(now);
        aiRequest.setUpdatedAt(now);
        aiRequest.setRequestTime(now);

        // 设置必填的状态和版本字段
        aiRequest.setStatus(RequestStatus.PROCESSING);
        aiRequest.setVersion(1);

        // 设置必填的ID字段
        aiRequest.setTenantId(request.getTenantId());
        aiRequest.setEmployeeId(request.getEmployeeId());

        // 设置软删除标志
        aiRequest.setIsDeleted(false);

        // 设置元数据，包含使用的模型列表
        try {
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("selectedModels", selectedModels);
            metadata.put("callType", "ensemble");
            // 这里简化处理，实际可能需要使用ObjectMapper
            aiRequest.setMetadata(metadata.toString());
        } catch (Exception e) {
            log.warn("设置元数据失败: {}", e.getMessage());
            aiRequest.setMetadata(null);
        }

        return aiRequest;
    }

    /**
     * 更新请求为成功状态
     */
    private void updateRequestWithSuccess(AiRequest aiRequest, String response, Instant responseTime) {
        // 截断响应内容，确保不超过数据库列的长度限制
        String content = response;
        if (content != null && content.length() > 4000) { // 假设数据库字段长度为4000
            content = content.substring(0, 3997) + "...";
        }

        aiRequest.setResponse(content);
        aiRequest.setStatus(RequestStatus.SUCCESS);
        aiRequest.setResponseTime(responseTime);
        aiRequest.setUpdatedAt(responseTime);
        requestRepository.save(aiRequest);
    }

    /**
     * 更新请求为失败状态
     */
    private void updateRequestWithError(AiRequest aiRequest, Exception e) {
        // 截断错误信息，确保不超过数据库列的长度限制
        String errorMessage = e.getMessage();
        if (errorMessage != null && errorMessage.length() > 1000) { // 假设错误信息字段长度为1000
            errorMessage = errorMessage.substring(0, 997) + "...";
        }

        aiRequest.setStatus(RequestStatus.FAILED);
        aiRequest.setErrorMessage(errorMessage);
        aiRequest.setResponseTime(Instant.now());
        aiRequest.setUpdatedAt(Instant.now());
        requestRepository.save(aiRequest);
    }

    /**
     * 保存多模型集成调用指标
     */
    private void saveEnsembleMetrics(
        AiInvocationRequestDTO request,
        List<String> selectedModels,
        AiRequest aiRequest,
        long totalDurationMs,
        List<ModelResponse> responses
    ) {
        try {
            // 为每个成功调用的模型保存指标
            for (ModelResponse response : responses) {
                String toolKey = response.getModelName();

                // 创建AiWorkflowResult用于指标保存
                com.whiskerguard.ai.service.workflow.AiWorkflowResult workflowResult =
                    new com.whiskerguard.ai.service.workflow.AiWorkflowResult(response.getResponse(), null, response.getResponseTime());

                // 调用统一的指标保存服务
                metricsService.saveAiCallMetrics(toolKey, workflowResult, aiRequest);
            }

            // 额外保存一个集成调用的总体指标
            com.whiskerguard.ai.service.workflow.AiWorkflowResult ensembleResult =
                new com.whiskerguard.ai.service.workflow.AiWorkflowResult(aiRequest.getResponse(), null, totalDurationMs);
            metricsService.saveAiCallMetrics("ensemble", ensembleResult, aiRequest);

            log.debug("已保存多模型集成调用指标，模型数量: {}, 总响应时间: {}ms", selectedModels.size(), totalDurationMs);
        } catch (Exception e) {
            log.error("保存多模型集成指标失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }
}
