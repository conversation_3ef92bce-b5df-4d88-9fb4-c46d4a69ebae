package com.whiskerguard.ai.service.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 法规摘要DTO
 * <p>
 * 用于提示词增强和AI响应优化，
 * 包含法规的摘要信息和关键要点。
 */
public class LawSummaryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 法规ID
     */
    private Long id;

    /**
     * 法规名称
     */
    private String title;

    /**
     * 法规类型
     */
    private String regulationType;

    /**
     * 法规摘要
     */
    private String summary;

    /**
     * 关键要点列表
     */
    private List<String> keyPoints;

    /**
     * 适用场景
     */
    private List<String> applicableScenarios;

    /**
     * 相关关键词
     */
    private List<String> keywords;

    /**
     * 分类
     */
    private String category;

    /**
     * 重要程度（1-5）
     */
    private Integer importance;

    /**
     * 是否为内部制度
     */
    private Boolean isInternal;

    // 构造函数
    public LawSummaryDTO() {}

    public LawSummaryDTO(Long id, String title, String regulationType, String summary) {
        this.id = id;
        this.title = title;
        this.regulationType = regulationType;
        this.summary = summary;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getRegulationType() {
        return regulationType;
    }

    public void setRegulationType(String regulationType) {
        this.regulationType = regulationType;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public List<String> getKeyPoints() {
        return keyPoints;
    }

    public void setKeyPoints(List<String> keyPoints) {
        this.keyPoints = keyPoints;
    }

    public List<String> getApplicableScenarios() {
        return applicableScenarios;
    }

    public void setApplicableScenarios(List<String> applicableScenarios) {
        this.applicableScenarios = applicableScenarios;
    }

    public List<String> getKeywords() {
        return keywords;
    }

    public void setKeywords(List<String> keywords) {
        this.keywords = keywords;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Integer getImportance() {
        return importance;
    }

    public void setImportance(Integer importance) {
        this.importance = importance;
    }

    public Boolean getIsInternal() {
        return isInternal;
    }

    public void setIsInternal(Boolean isInternal) {
        this.isInternal = isInternal;
    }

    @Override
    public String toString() {
        return "LawSummaryDTO{" +
            "id=" + id +
            ", title='" + title + '\'' +
            ", regulationType='" + regulationType + '\'' +
            ", summary='" + summary + '\'' +
            ", category='" + category + '\'' +
            ", importance=" + importance +
            ", isInternal=" + isInternal +
            '}';
    }
}
