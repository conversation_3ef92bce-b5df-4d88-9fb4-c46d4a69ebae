package com.whiskerguard.ai.service;

import com.whiskerguard.ai.client.RegulatoryServiceClient;
import com.whiskerguard.ai.service.dto.EnhancedAiResponse;
import com.whiskerguard.ai.service.dto.LawReferenceDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 法规引用增强器服务
 * <p>
 * 负责在AI响应中识别和标记法规引用，
 * 提供带有法规引用链接的增强AI响应。
 */
@Service
public class LawReferenceEnhancer {

    private final Logger log = LoggerFactory.getLogger(LawReferenceEnhancer.class);

    private final RegulatoryServiceClient regulatoryServiceClient;

    // 法规关键词模式
    private static final List<Pattern> LAW_PATTERNS = Arrays.asList(
        Pattern.compile("《([^》]+法)》(?:第([\\d]+)条)?"),           // 《合同法》第52条
        Pattern.compile("《([^》]+条例)》(?:第([\\d]+)条)?"),         // 《公司登记管理条例》第10条
        Pattern.compile("《([^》]+规定)》(?:第([\\d]+)条)?"),         // 《企业信息公示暂行条例》第8条
        Pattern.compile("《([^》]+办法)》(?:第([\\d]+)条)?"),         // 《合同违法行为监督处理办法》第6条
        Pattern.compile("《([^》]+制度)》(?:第([\\d\\.]+)条)?"),      // 《采购管理制度》第3.2条
        Pattern.compile("([\\u4e00-\\u9fa5]+法)第([\\d]+)条"),       // 合同法第52条
        Pattern.compile("([\\u4e00-\\u9fa5]+条例)第([\\d]+)条")      // 公司法第25条
    );

    // 法律概念关键词
    private static final Set<String> LAW_KEYWORDS = Set.of(
        "合同", "协议", "违约", "责任", "义务", "权利", "损害赔偿", "违法", "合规",
        "法律", "法规", "条例", "规定", "办法", "制度", "标准", "规范",
        "民法典", "合同法", "公司法", "劳动法", "消费者权益保护法",
        "无效", "撤销", "解除", "终止", "履行", "违反", "侵权"
    );

    public LawReferenceEnhancer(RegulatoryServiceClient regulatoryServiceClient) {
        this.regulatoryServiceClient = regulatoryServiceClient;
    }

    /**
     * 增强AI响应，添加法规引用
     *
     * @param aiResponse AI原始响应内容
     * @param tenantId   租户ID
     * @return 增强后的AI响应
     */
    public EnhancedAiResponse enhanceWithReferences(String aiResponse, Long tenantId) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理AI响应法规引用增强，租户ID: {}, 响应长度: {}", tenantId, aiResponse.length());

        try {
            // 1. 提取法规关键词
            List<String> legalKeywords = extractLawKeywords(aiResponse);
            log.debug("提取到法规关键词: {}", legalKeywords);

            if (legalKeywords.isEmpty()) {
                log.info("未找到法规关键词，返回原始响应");
                return createNoReferencesResponse(aiResponse, startTime);
            }

            // 2. 调用regulatory服务查找相关法规
            List<LawReferenceDTO> references = findRelevantLaws(legalKeywords, tenantId);
            log.debug("找到相关法规: {} 条", references.size());

            if (references.isEmpty()) {
                log.info("未找到相关法规，返回原始响应");
                return createNoReferencesResponse(aiResponse, startTime);
            }

            // 3. 在AI响应中添加引用标记
            String enhancedContent = addReferenceMarkers(aiResponse, references);

            // 4. 构建引用映射
            Map<String, String> referenceMap = buildReferenceMap(references);

            // 5. 计算统计信息
            EnhancedAiResponse.ReferenceStatistics statistics = calculateStatistics(references);

            // 6. 构建增强响应
            EnhancedAiResponse enhancedResponse = new EnhancedAiResponse(aiResponse, enhancedContent, references);
            enhancedResponse.setReferenceMap(referenceMap);
            enhancedResponse.setStatistics(statistics);
            enhancedResponse.setStatus(EnhancedAiResponse.ProcessingStatus.SUCCESS);
            enhancedResponse.setProcessingTimeMs(System.currentTimeMillis() - startTime);

            log.info("法规引用增强完成，处理时间: {}ms, 引用数量: {}", 
                    enhancedResponse.getProcessingTimeMs(), references.size());

            return enhancedResponse;

        } catch (Exception e) {
            log.error("法规引用增强处理失败，租户ID: {}, 错误: {}", tenantId, e.getMessage(), e);
            return createErrorResponse(aiResponse, e.getMessage(), startTime);
        }
    }

    /**
     * 从AI响应中提取法规关键词
     */
    private List<String> extractLawKeywords(String content) {
        Set<String> keywords = new HashSet<>();

        // 1. 使用正则表达式匹配法规名称
        for (Pattern pattern : LAW_PATTERNS) {
            Matcher matcher = pattern.matcher(content);
            while (matcher.find()) {
                String lawName = matcher.group(1);
                keywords.add(lawName);
                
                // 如果有条款号，也加入关键词
                if (matcher.groupCount() > 1 && matcher.group(2) != null) {
                    keywords.add(lawName + "第" + matcher.group(2) + "条");
                }
            }
        }

        // 2. 匹配法律概念关键词
        for (String keyword : LAW_KEYWORDS) {
            if (content.contains(keyword)) {
                keywords.add(keyword);
            }
        }

        return new ArrayList<>(keywords);
    }

    /**
     * 调用regulatory服务查找相关法规
     */
    private List<LawReferenceDTO> findRelevantLaws(List<String> keywords, Long tenantId) {
        List<LawReferenceDTO> references = regulatoryServiceClient.findRelevantRegulations(keywords, tenantId);

        // 按相关性得分排序，取前10个
        return references.stream()
            .sorted((a, b) -> Double.compare(
                b.getRelevanceScore() != null ? b.getRelevanceScore() : 0.0,
                a.getRelevanceScore() != null ? a.getRelevanceScore() : 0.0
            ))
            .limit(10)
            .collect(Collectors.toList());
    }

    /**
     * 在AI响应中添加引用标记
     */
    private String addReferenceMarkers(String content, List<LawReferenceDTO> references) {
        String enhancedContent = content;
        int referenceIndex = 1;

        for (LawReferenceDTO reference : references) {
            String markerId = "ref_" + referenceIndex;
            reference.setReferenceMarkerId(markerId);

            // 构建引用文本
            String referenceText = buildReferenceText(reference);
            
            // 在内容中查找并替换
            String pattern = Pattern.quote(referenceText);
            String replacement = String.format(
                "<span class=\"law-ref\" data-ref-id=\"%s\" onclick=\"showReference('%s')\">%s</span>",
                markerId, markerId, referenceText
            );
            
            enhancedContent = enhancedContent.replaceFirst(pattern, replacement);
            referenceIndex++;
        }

        return enhancedContent;
    }

    /**
     * 构建引用文本
     */
    private String buildReferenceText(LawReferenceDTO reference) {
        StringBuilder text = new StringBuilder();
        text.append("《").append(reference.getTitle()).append("》");
        
        if (reference.getArticleNumber() != null && !reference.getArticleNumber().isEmpty()) {
            text.append("第").append(reference.getArticleNumber()).append("条");
        }
        
        return text.toString();
    }

    /**
     * 构建引用映射
     */
    private Map<String, String> buildReferenceMap(List<LawReferenceDTO> references) {
        return references.stream()
            .collect(Collectors.toMap(
                LawReferenceDTO::getReferenceMarkerId,
                this::buildReferenceText,
                (existing, replacement) -> existing
            ));
    }

    /**
     * 计算统计信息
     */
    private EnhancedAiResponse.ReferenceStatistics calculateStatistics(List<LawReferenceDTO> references) {
        EnhancedAiResponse.ReferenceStatistics stats = new EnhancedAiResponse.ReferenceStatistics();
        
        stats.setTotalReferences(references.size());
        stats.setLawReferences((int) references.stream().filter(r -> "LAW".equals(r.getRegulationType())).count());
        stats.setRegulationReferences((int) references.stream().filter(r -> "REGULATION".equals(r.getRegulationType())).count());
        stats.setInternalReferences((int) references.stream().filter(r -> Boolean.TRUE.equals(r.getIsInternal())).count());
        
        double avgScore = references.stream()
            .mapToDouble(r -> r.getRelevanceScore() != null ? r.getRelevanceScore() : 0.0)
            .average()
            .orElse(0.0);
        stats.setAverageRelevanceScore(avgScore);
        
        return stats;
    }

    /**
     * 创建无引用响应
     */
    private EnhancedAiResponse createNoReferencesResponse(String originalContent, long startTime) {
        EnhancedAiResponse response = new EnhancedAiResponse();
        response.setOriginalContent(originalContent);
        response.setEnhancedContent(originalContent);
        response.setReferences(Collections.emptyList());
        response.setReferenceMap(Collections.emptyMap());
        response.setStatus(EnhancedAiResponse.ProcessingStatus.NO_REFERENCES);
        response.setProcessingTimeMs(System.currentTimeMillis() - startTime);
        return response;
    }

    /**
     * 创建错误响应
     */
    private EnhancedAiResponse createErrorResponse(String originalContent, String errorMessage, long startTime) {
        EnhancedAiResponse response = new EnhancedAiResponse();
        response.setOriginalContent(originalContent);
        response.setEnhancedContent(originalContent);
        response.setReferences(Collections.emptyList());
        response.setReferenceMap(Collections.emptyMap());
        response.setStatus(EnhancedAiResponse.ProcessingStatus.FAILED);
        response.setErrorMessage(errorMessage);
        response.setProcessingTimeMs(System.currentTimeMillis() - startTime);
        return response;
    }
}
