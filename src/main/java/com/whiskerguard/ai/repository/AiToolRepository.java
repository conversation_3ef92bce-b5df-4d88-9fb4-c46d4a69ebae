// AiToolRepository.java
package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.AiTool;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AiToolRepository extends JpaRepository<AiTool, Long> {
    /**
     * 根据工具唯一 key 查配置
     */
    Optional<AiTool> findByToolKey(String toolKey);

    /**
     * 根据工具键查找未删除的AI工具
     * @param toolKey 工具键
     * @return 未删除的AI工具
     */
    Optional<AiTool> findByToolKeyAndIsDeletedFalse(String toolKey);

    /**
     * 根据工具键查找未删除且非模型类型的AI工具
     * @param toolKey 工具键
     * @return 未删除且非模型类型的AI工具
     */
    Optional<AiTool> findByToolKeyAndIsDeletedFalseAndIsModelFalse(String toolKey);

    /**
     * 查找所有未删除且状态为启用的AI工具
     * @return 可用的AI工具列表
     */
    List<AiTool> findByIsDeletedFalseAndStatus(com.whiskerguard.ai.domain.enumeration.ToolStatus status);

    /**
     * 根据租户ID查找AI工具
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 分页的AI工具列表
     */
    Page<AiTool> findByTenantIdAndIsDeletedFalse(Long tenantId, Pageable pageable);

    /**
     * 查找所有未删除且为模型类型的AI工具
     * @param isModel 是否为模型类型
     * @param pageable 分页参数
     * @return 分页的模型列表
     */
    Page<AiTool> findByIsModelAndIsDeletedFalse(Boolean isModel, Pageable pageable);

    /**
     * 根据模型分类查找未删除的模型
     * @param isModel 是否为模型类型
     * @param modelCategory 模型分类
     * @param pageable 分页参数
     * @return 分页的模型列表
     */
    Page<AiTool> findByIsModelAndModelCategoryAndIsDeletedFalse(Boolean isModel, String modelCategory, Pageable pageable);
}
