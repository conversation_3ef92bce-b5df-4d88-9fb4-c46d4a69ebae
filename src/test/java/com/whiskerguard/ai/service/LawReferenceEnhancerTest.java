package com.whiskerguard.ai.service;

import com.whiskerguard.ai.client.RegulatoryServiceClient;
import com.whiskerguard.ai.service.dto.EnhancedAiResponse;
import com.whiskerguard.ai.service.dto.LawReferenceDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * 法规引用增强器测试类
 */
@ExtendWith(MockitoExtension.class)
class LawReferenceEnhancerTest {

    @Mock
    private RegulatoryServiceClient regulatoryServiceClient;

    private LawReferenceEnhancer lawReferenceEnhancer;

    @BeforeEach
    void setUp() {
        lawReferenceEnhancer = new LawReferenceEnhancer(regulatoryServiceClient);
    }

    @Test
    void testEnhanceWithReferences_Success() {
        // Given
        String aiResponse = "根据《合同法》第52条的规定，该合同条款存在显失公平的情况。";
        Long tenantId = 1L;

        LawReferenceDTO reference = new LawReferenceDTO();
        reference.setId(1L);
        reference.setTitle("合同法");
        reference.setArticleNumber("52");
        reference.setRegulationType("LAW");
        reference.setSummary("有下列情形之一的，合同无效...");
        reference.setRelevanceScore(95.0);

        when(regulatoryServiceClient.findRelevantRegulations(any(), eq(tenantId)))
            .thenReturn(Arrays.asList(reference));

        // When
        EnhancedAiResponse result = lawReferenceEnhancer.enhanceWithReferences(aiResponse, tenantId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(EnhancedAiResponse.ProcessingStatus.SUCCESS);
        assertThat(result.getOriginalContent()).isEqualTo(aiResponse);
        assertThat(result.getReferences()).hasSize(1);
        assertThat(result.getReferences().get(0).getTitle()).isEqualTo("合同法");
        assertThat(result.getProcessingTimeMs()).isGreaterThan(0);
    }

    @Test
    void testEnhanceWithReferences_NoKeywords() {
        // Given
        String aiResponse = "这是一个普通的AI响应，没有法律相关内容。";
        Long tenantId = 1L;

        // When
        EnhancedAiResponse result = lawReferenceEnhancer.enhanceWithReferences(aiResponse, tenantId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(EnhancedAiResponse.ProcessingStatus.NO_REFERENCES);
        assertThat(result.getOriginalContent()).isEqualTo(aiResponse);
        assertThat(result.getEnhancedContent()).isEqualTo(aiResponse);
        assertThat(result.getReferences()).isEmpty();
    }

    @Test
    void testEnhanceWithReferences_NoRelevantLaws() {
        // Given
        String aiResponse = "根据合同法的相关规定，需要注意合规问题。";
        Long tenantId = 1L;

        when(regulatoryServiceClient.findRelevantRegulations(any(), eq(tenantId)))
            .thenReturn(Collections.emptyList());

        // When
        EnhancedAiResponse result = lawReferenceEnhancer.enhanceWithReferences(aiResponse, tenantId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(EnhancedAiResponse.ProcessingStatus.NO_REFERENCES);
        assertThat(result.getOriginalContent()).isEqualTo(aiResponse);
        assertThat(result.getEnhancedContent()).isEqualTo(aiResponse);
        assertThat(result.getReferences()).isEmpty();
    }

    @Test
    void testEnhanceWithReferences_ServiceException() {
        // Given
        String aiResponse = "根据《合同法》第52条的规定，该合同条款存在显失公平的情况。";
        Long tenantId = 1L;

        when(regulatoryServiceClient.findRelevantRegulations(any(), eq(tenantId)))
            .thenThrow(new RuntimeException("Service unavailable"));

        // When
        EnhancedAiResponse result = lawReferenceEnhancer.enhanceWithReferences(aiResponse, tenantId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(EnhancedAiResponse.ProcessingStatus.FAILED);
        assertThat(result.getOriginalContent()).isEqualTo(aiResponse);
        assertThat(result.getEnhancedContent()).isEqualTo(aiResponse);
        assertThat(result.getErrorMessage()).contains("Service unavailable");
    }

    @Test
    void testEnhanceWithReferences_MultipleReferences() {
        // Given
        String aiResponse = "根据《合同法》第52条和《民法典》第151条的规定，该合同条款需要修改。";
        Long tenantId = 1L;

        LawReferenceDTO reference1 = new LawReferenceDTO();
        reference1.setId(1L);
        reference1.setTitle("合同法");
        reference1.setArticleNumber("52");
        reference1.setRegulationType("LAW");
        reference1.setRelevanceScore(95.0);

        LawReferenceDTO reference2 = new LawReferenceDTO();
        reference2.setId(2L);
        reference2.setTitle("民法典");
        reference2.setArticleNumber("151");
        reference2.setRegulationType("LAW");
        reference2.setRelevanceScore(90.0);

        when(regulatoryServiceClient.findRelevantRegulations(any(), eq(tenantId)))
            .thenReturn(Arrays.asList(reference1, reference2));

        // When
        EnhancedAiResponse result = lawReferenceEnhancer.enhanceWithReferences(aiResponse, tenantId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(EnhancedAiResponse.ProcessingStatus.SUCCESS);
        assertThat(result.getReferences()).hasSize(2);
        assertThat(result.getStatistics().getTotalReferences()).isEqualTo(2);
        assertThat(result.getStatistics().getLawReferences()).isEqualTo(2);
        assertThat(result.getStatistics().getAverageRelevanceScore()).isEqualTo(92.5);
    }

    @Test
    void testEnhanceWithReferences_InternalPolicy() {
        // Given
        String aiResponse = "根据公司《采购管理制度》第3.2条的规定，需要进行审批。";
        Long tenantId = 1L;

        LawReferenceDTO reference = new LawReferenceDTO();
        reference.setId(1L);
        reference.setTitle("采购管理制度");
        reference.setArticleNumber("3.2");
        reference.setRegulationType("INTERNAL_POLICY");
        reference.setIsInternal(true);
        reference.setRelevanceScore(98.0);

        when(regulatoryServiceClient.findRelevantRegulations(any(), eq(tenantId)))
            .thenReturn(Arrays.asList(reference));

        // When
        EnhancedAiResponse result = lawReferenceEnhancer.enhanceWithReferences(aiResponse, tenantId);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatus()).isEqualTo(EnhancedAiResponse.ProcessingStatus.SUCCESS);
        assertThat(result.getReferences()).hasSize(1);
        assertThat(result.getReferences().get(0).getIsInternal()).isTrue();
        assertThat(result.getStatistics().getInternalReferences()).isEqualTo(1);
    }
}
